"""
预测特定条件组合下缺失片数的RT值
专门用于处理用户指定的条件组合和缺失片数预测
"""

import pandas as pd
import numpy as np
from rt_prediction_model import RTPredictor, get_output_path
import os
from datetime import datetime

def predict_missing_qty_for_condition(equip, sub_equip, capability, recipe, 
                                     existing_data, predictor, export_path=None):
    """
    为特定条件组合预测缺失片数的rt值
    
    Args:
        equip, sub_equip, capability, recipe: 条件组合
        existing_data: 字典，键为qty，值为rt
        predictor: 已训练的预测器
        export_path: 导出文件路径
    
    Returns:
        包含实际值和预测值的完整DataFrame
    """
    print(f"\n{'='*60}")
    print(f"Predicting Missing Qty for Condition:")
    print(f"Equipment: {equip}")
    print(f"Sub-Equipment: {sub_equip}")
    print(f"Capability: {capability}")
    print(f"Recipe: {recipe}")
    print(f"{'='*60}")
    
    # 定义完整的qty范围（1-25）
    full_qty_range = list(range(1, 26))
    existing_qtys = list(existing_data.keys())
    missing_qtys = [qty for qty in full_qty_range if qty not in existing_qtys]
    
    print(f"\nExisting qty values: {sorted(existing_qtys)}")
    print(f"Missing qty values: {sorted(missing_qtys)}")
    print(f"Total missing: {len(missing_qtys)} out of 25")
    
    all_results = []
    
    # 添加现有数据（实际值）
    for qty in existing_qtys:
        rt = existing_data[qty]
        result_row = {
            'equip': equip,
            'sub_equip': sub_equip,
            'capability': capability,
            'recipe': recipe,
            'qty': qty,
            'rt': rt,
            'result_type': 'actual'
        }
        all_results.append(result_row)
    
    # 预测缺失的qty值
    if missing_qtys:
        print(f"\nPredicting RT values for missing qty: {sorted(missing_qtys)}")
        
        # 创建预测数据
        missing_data = pd.DataFrame([
            {
                'equip': equip,
                'sub_equip': sub_equip,
                'capability': capability,
                'recipe': recipe,
                'qty': qty
            } for qty in missing_qtys
        ])
        
        try:
            # 进行预测
            predictions = predictor.predict(missing_data)
            print(f"✓ Successfully predicted {len(predictions)} values")
            
            # 添加预测结果
            for qty, pred_rt in zip(missing_qtys, predictions):
                result_row = {
                    'equip': equip,
                    'sub_equip': sub_equip,
                    'capability': capability,
                    'recipe': recipe,
                    'qty': qty,
                    'rt': pred_rt,
                    'result_type': 'predicted'
                }
                all_results.append(result_row)
                
        except Exception as e:
            print(f"✗ Error during prediction: {e}")
            return None
    
    # 创建结果DataFrame并排序
    result_df = pd.DataFrame(all_results)
    result_df = result_df.sort_values(by='qty').reset_index(drop=True)
    
    # 导出结果
    if export_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"prediction_results_{equip}_{sub_equip}_{capability}_{recipe}_{timestamp}.csv"
        export_path = get_output_path(filename, "predictions")

    result_df.to_csv(export_path, index=False)
    print(f"\n✓ Results exported to: {export_path}")
    
    # 显示详细结果
    print(f"\n{'='*60}")
    print(f"COMPLETE RESULTS")
    print(f"{'='*60}")
    print(f"{'Qty':<5} {'RT':<12} {'Type':<12} {'Status'}")
    print(f"{'-'*5} {'-'*12} {'-'*12} {'-'*20}")
    
    for _, row in result_df.iterrows():
        qty = row['qty']
        rt = row['rt']
        result_type = row['result_type']
        status = "✓ Actual" if result_type == 'actual' else "🔮 Predicted"
        print(f"{qty:<5} {rt:<12.2f} {result_type:<12} {status}")
    
    # 统计信息
    actual_count = (result_df['result_type'] == 'actual').sum()
    predicted_count = (result_df['result_type'] == 'predicted').sum()
    
    print(f"\n{'='*60}")
    print(f"SUMMARY")
    print(f"{'='*60}")
    print(f"Total records: {len(result_df)}")
    print(f"Actual values: {actual_count}")
    print(f"Predicted values: {predicted_count}")
    print(f"Coverage: {len(result_df)}/25 qty values ({len(result_df)/25*100:.1f}%)")
    
    return result_df

def load_trained_model(model_path="rt_prediction_model"):
    """加载已训练的模型"""
    predictor = RTPredictor()
    try:
        predictor.load_model(model_path)
        print(f"✓ Model loaded successfully from {model_path}")
        return predictor
    except Exception as e:
        print(f"✗ Failed to load model: {e}")
        print("Please ensure you have trained the model first by running example_usage.py")
        return None

def main_example():
    """主要示例：根据您的具体需求进行预测"""
    print("RT Prediction for Missing Qty Values")
    print("="*60)
    
    # 加载模型
    predictor = load_trained_model()
    if predictor is None:
        return
    
    # 示例1：您提到的具体情况
    print("\nExample 1: Your specific case")
    print("Condition with existing qty: 1,2,3,4,6,8,9,10,15,16,17,18,19,20,25")
    print("Missing qty to predict: 5,7,11,12,13,14,21,22,23,24")
    
    # 现有数据（您需要替换为实际数据）
    existing_data_1 = {
        1: 8.5, 2: 9.2, 3: 10.1, 4: 11.3, 6: 13.8, 8: 16.2, 9: 17.1, 10: 18.5,
        15: 25.8, 16: 27.2, 17: 28.6, 18: 30.1, 19: 31.5, 20: 33.2, 25: 42.1
    }
    
    result_1 = predict_missing_qty_for_condition(
        equip='EQ001',
        sub_equip='SUB_A',
        capability='CAP_HIGH',
        recipe='RECIPE_1',
        existing_data=existing_data_1,
        predictor=predictor,
        export_path="example_1_complete_results.csv"
    )
    
    # 示例2：另一个条件组合
    print(f"\n{'='*80}")
    print("\nExample 2: Another condition combination")
    
    existing_data_2 = {
        2: 12.1, 5: 15.3, 8: 18.7, 12: 23.4, 16: 28.9, 20: 34.2, 24: 39.8
    }
    
    result_2 = predict_missing_qty_for_condition(
        equip='EQ002',
        sub_equip='SUB_B',
        capability='CAP_MED',
        recipe='RECIPE_2',
        existing_data=existing_data_2,
        predictor=predictor,
        export_path="example_2_complete_results.csv"
    )
    
    print(f"\n{'='*80}")
    print("BATCH PROCESSING COMPLETE")
    print(f"{'='*80}")
    print("Generated files:")
    print("- example_1_complete_results.csv")
    print("- example_2_complete_results.csv")
    print("\nEach file contains:")
    print("- All qty values from 1 to 25")
    print("- Corresponding rt values (actual or predicted)")
    print("- result_type column indicating 'actual' or 'predicted'")

def custom_prediction(equip, sub_equip, capability, recipe, existing_data_dict):
    """
    自定义预测函数，供用户直接调用
    
    Args:
        equip, sub_equip, capability, recipe: 条件组合
        existing_data_dict: 现有数据字典 {qty: rt, ...}
    
    Returns:
        完整的预测结果DataFrame
    """
    predictor = load_trained_model()
    if predictor is None:
        return None
    
    return predict_missing_qty_for_condition(
        equip, sub_equip, capability, recipe, 
        existing_data_dict, predictor
    )

if __name__ == "__main__":
    main_example()
    
    print(f"\n{'='*80}")
    print("HOW TO USE THIS SCRIPT:")
    print(f"{'='*80}")
    print("1. Ensure you have trained the model first (run example_usage.py)")
    print("2. Modify the existing_data dictionaries with your actual data")
    print("3. Update the condition parameters (equip, sub_equip, etc.)")
    print("4. Run this script to get complete predictions")
    print("5. Check the generated CSV files for results")
    print("\nFor custom usage:")
    print("from predict_missing_qty import custom_prediction")
    print("result = custom_prediction('EQ001', 'SUB_A', 'CAP_HIGH', 'RECIPE_1', {1: 8.5, 2: 9.2, ...})")
