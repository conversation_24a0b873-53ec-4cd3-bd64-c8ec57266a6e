import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 配置matplotlib中文字体
import matplotlib
import platform

def setup_chinese_font():
    """配置matplotlib中文字体"""
    system = platform.system()
    if system == "Windows":
        # Windows系统常用中文字体
        fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        fonts = ['Arial Unicode MS', 'Heiti TC', 'STHeiti']
    else:  # Linux
        fonts = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'SimHei']

    for font in fonts:
        try:
            matplotlib.rcParams['font.sans-serif'] = [font]
            matplotlib.rcParams['axes.unicode_minus'] = False
            # 测试字体是否可用
            fig, ax = plt.subplots(figsize=(1, 1))
            ax.text(0.5, 0.5, '测试', fontsize=12)
            plt.close(fig)
            print(f"使用字体: {font}")
            break
        except:
            continue
    else:
        print("警告: 未找到合适的中文字体，中文可能显示为方框")
        # 使用默认字体
        matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans']
        matplotlib.rcParams['axes.unicode_minus'] = False

# 初始化字体配置
setup_chinese_font()

class RTDataAnalyzer:
    """
    运行时间数据分析工具
    用于分析数据分布、相关性和决定建模策略
    """
    
    def __init__(self, df: pd.DataFrame):
        """
        初始化分析器
        
        Args:
            df: 包含equip, sub_equip, capability, recipe, qty, rt列的DataFrame
        """
        self.df = df.copy()
        self.condition_stats = None
        
    def basic_statistics(self):
        """基础统计分析"""
        print("=== Basic Statistics ===")
        print(f"Total records: {len(self.df)}")
        print(f"Data range: qty={self.df['qty'].min()}-{self.df['qty'].max()}, rt={self.df['rt'].min():.2f}-{self.df['rt'].max():.2f}")
        print("\nBasic statistics for all columns:")
        print(self.df.describe())

        print("\nCategorical variable distribution:")
        categorical_cols = ['equip', 'sub_equip', 'capability', 'recipe']
        for col in categorical_cols:
            print(f"\n{col}:")
            print(self.df[col].value_counts())
    
    def analyze_rt_distribution(self):
        """分析rt分布特征"""
        print("\n=== RT Distribution Analysis ===")

        # 整体分布
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 直方图
        axes[0,0].hist(self.df['rt'], bins=50, alpha=0.7, edgecolor='black')
        axes[0,0].set_title('RT Distribution Histogram')
        axes[0,0].set_xlabel('RT')
        axes[0,0].set_ylabel('Frequency')

        # Q-Q图检验正态性
        stats.probplot(self.df['rt'], dist="norm", plot=axes[0,1])
        axes[0,1].set_title('RT Normality Test (Q-Q Plot)')

        # 箱线图按qty分组
        qty_groups = pd.cut(self.df['qty'], bins=5, labels=['1-5', '6-10', '11-15', '16-20', '21-25'])
        self.df['qty_group'] = qty_groups
        sns.boxplot(data=self.df, x='qty_group', y='rt', ax=axes[1,0])
        axes[1,0].set_title('RT Distribution by Qty Range')
        axes[1,0].tick_params(axis='x', rotation=45)

        # RT vs qty散点图
        axes[1,1].scatter(self.df['qty'], self.df['rt'], alpha=0.6)
        axes[1,1].set_xlabel('qty')
        axes[1,1].set_ylabel('rt')
        axes[1,1].set_title('RT vs Qty Scatter Plot')
        
        plt.tight_layout()
        plt.show()
        
        # 统计检验
        shapiro_stat, shapiro_p = stats.shapiro(self.df['rt'].sample(min(5000, len(self.df))))
        print(f"Shapiro-Wilk normality test: statistic={shapiro_stat:.4f}, p-value={shapiro_p:.4f}")
        if shapiro_p < 0.05:
            print("RT distribution significantly deviates from normal, consider data transformation")
        else:
            print("RT distribution is approximately normal")
    
    def analyze_condition_combinations(self):
        """分析不同条件组合的统计特征"""
        print("\n=== Condition Combination Analysis ===")

        # 创建条件组合
        condition_cols = ['equip', 'sub_equip', 'capability', 'recipe']
        self.df['condition_key'] = self.df[condition_cols].astype(str).agg('_'.join, axis=1)

        # 计算每个组合的统计信息
        self.condition_stats = self.df.groupby('condition_key').agg({
            'rt': ['count', 'mean', 'median', 'std', 'min', 'max'],
            'qty': ['min', 'max', 'nunique']
        }).round(4)

        self.condition_stats.columns = ['_'.join(col).strip() for col in self.condition_stats.columns]
        self.condition_stats = self.condition_stats.reset_index()

        print(f"Total {len(self.condition_stats)} different condition combinations")
        print(f"Average {self.condition_stats['rt_count'].mean():.1f} records per combination")

        # 显示数据量最多和最少的组合
        print("\nTop 5 combinations with most data:")
        top_combinations = self.condition_stats.nlargest(5, 'rt_count')
        print(top_combinations[['condition_key', 'rt_count', 'rt_mean', 'rt_std']])

        print("\nTop 5 combinations with least data:")
        bottom_combinations = self.condition_stats.nsmallest(5, 'rt_count')
        print(bottom_combinations[['condition_key', 'rt_count', 'rt_mean', 'rt_std']])
        
        # 可视化
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 每种组合的数据量分布
        axes[0,0].hist(self.condition_stats['rt_count'], bins=20, alpha=0.7, edgecolor='black')
        axes[0,0].set_title('Data Count Distribution by Condition')
        axes[0,0].set_xlabel('Data Count')
        axes[0,0].set_ylabel('Number of Combinations')

        # RT均值的分布
        axes[0,1].hist(self.condition_stats['rt_mean'], bins=20, alpha=0.7, edgecolor='black')
        axes[0,1].set_title('RT Mean Distribution by Combination')
        axes[0,1].set_xlabel('RT Mean')
        axes[0,1].set_ylabel('Number of Combinations')

        # RT标准差的分布
        axes[1,0].hist(self.condition_stats['rt_std'].dropna(), bins=20, alpha=0.7, edgecolor='black')
        axes[1,0].set_title('RT Std Distribution by Combination')
        axes[1,0].set_xlabel('RT Standard Deviation')
        axes[1,0].set_ylabel('Number of Combinations')

        # 数据量 vs RT变异性
        valid_std = self.condition_stats['rt_std'].notna()
        axes[1,1].scatter(self.condition_stats.loc[valid_std, 'rt_count'],
                         self.condition_stats.loc[valid_std, 'rt_std'], alpha=0.6)
        axes[1,1].set_xlabel('Data Count')
        axes[1,1].set_ylabel('RT Standard Deviation')
        axes[1,1].set_title('Data Count vs RT Variability')
        
        plt.tight_layout()
        plt.show()
    
    def recommend_modeling_strategy(self):
        """基于数据分析推荐建模策略"""
        print("\n=== Modeling Strategy Recommendations ===")

        if self.condition_stats is None:
            self.analyze_condition_combinations()

        # 分析数据特征
        # After analyze_condition_combinations(), condition_stats is guaranteed to be a DataFrame
        assert self.condition_stats is not None, "condition_stats should not be None after analyze_condition_combinations()"
        total_combinations = len(self.condition_stats)
        avg_samples_per_combination = self.condition_stats['rt_count'].mean()
        combinations_with_few_samples = (self.condition_stats['rt_count'] < 5).sum()
        high_variance_combinations = (self.condition_stats['rt_std'] > self.condition_stats['rt_std'].quantile(0.8)).sum()

        print(f"Data characteristics analysis:")
        print(f"- Total condition combinations: {total_combinations}")
        print(f"- Average samples per combination: {avg_samples_per_combination:.1f}")
        print(f"- Combinations with <5 samples: {combinations_with_few_samples} ({combinations_with_few_samples/total_combinations*100:.1f}%)")
        print(f"- High variance combinations: {high_variance_combinations} ({high_variance_combinations/total_combinations*100:.1f}%)")

        print(f"\nModeling strategy recommendations:")

        # 策略1: 数据使用建议
        if avg_samples_per_combination >= 10:
            print("✓ Recommend using raw data for training, sufficient data volume")
        else:
            print("⚠ Recommend combining raw data with statistical features, some combinations have limited data")

        # 策略2: 特征工程建议
        if combinations_with_few_samples / total_combinations > 0.3:
            print("✓ Recommend adding historical statistical features (mean, median, etc.) as supplements")
            print("✓ Consider using hierarchical modeling or transfer learning")

        if high_variance_combinations / total_combinations > 0.2:
            print("✓ Recommend adding uncertainty estimation (e.g., confidence intervals)")
            print("✓ Consider using ensemble methods for improved robustness")

        # 策略3: 模型复杂度建议
        if total_combinations > 100:
            print("✓ Many condition combinations, recommend using deep learning models")
        else:
            print("✓ Consider traditional machine learning methods (e.g., Random Forest, XGBoost)")

        # 策略4: 验证策略建议
        print("✓ Recommend using stratified sampling to ensure all condition combinations are represented in train/test sets")
        print("✓ Recommend cross-validation to assess model stability")
        
        return {
            'use_raw_data': avg_samples_per_combination >= 10,
            'add_statistical_features': combinations_with_few_samples / total_combinations > 0.3,
            'add_uncertainty': high_variance_combinations / total_combinations > 0.2,
            'use_deep_learning': total_combinations > 100,
            'avg_samples_per_combination': avg_samples_per_combination
        }
    
    def correlation_analysis(self):
        """相关性分析"""
        print("\n=== Correlation Analysis ===")

        # 编码分类变量进行相关性分析
        df_encoded = self.df.copy()
        from sklearn.preprocessing import LabelEncoder

        categorical_cols = ['equip', 'sub_equip', 'capability', 'recipe']
        for col in categorical_cols:
            le = LabelEncoder()
            df_encoded[f'{col}_encoded'] = le.fit_transform(df_encoded[col].astype(str))

        # 计算相关性矩阵
        numeric_cols = ['equip_encoded', 'sub_equip_encoded', 'capability_encoded',
                       'recipe_encoded', 'qty', 'rt']
        corr_matrix = df_encoded[numeric_cols].corr()

        # 可视化相关性矩阵
        plt.figure(figsize=(10, 8))
        sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5)
        plt.title('Feature Correlation Matrix')
        plt.tight_layout()
        plt.show()

        # 分析与rt的相关性
        rt_correlations = corr_matrix['rt'].abs().sort_values(ascending=False)
        print("Correlation with RT (sorted):")
        for feature, corr in rt_correlations.items():
            if feature != 'rt':
                print(f"{feature}: {corr:.4f}")


# 使用示例
if __name__ == "__main__":
    # 使用rt_prediction_model.py中的示例数据生成代码
    import sys
    sys.path.append('.')
    
    # 生成示例数据
    np.random.seed(42)
    n_samples = 1000
    equips = ['EQ001', 'EQ002', 'EQ003', 'EQ004']
    sub_equips = ['SUB_A', 'SUB_B', 'SUB_C']
    capabilities = ['CAP_HIGH', 'CAP_MED', 'CAP_LOW']
    recipes = ['RECIPE_1', 'RECIPE_2', 'RECIPE_3', 'RECIPE_4', 'RECIPE_5']
    
    data = []
    for _ in range(n_samples):
        equip = np.random.choice(equips)
        sub_equip = np.random.choice(sub_equips)
        capability = np.random.choice(capabilities)
        recipe = np.random.choice(recipes)
        qty = np.random.randint(1, 26)
        
        # 模拟rt与各因素的关系
        base_rt = 10
        equip_factor = {'EQ001': 1.0, 'EQ002': 1.2, 'EQ003': 0.9, 'EQ004': 1.1}[equip]
        cap_factor = {'CAP_HIGH': 0.8, 'CAP_MED': 1.0, 'CAP_LOW': 1.3}[capability]
        recipe_factor = np.random.uniform(0.8, 1.5)
        qty_factor = qty * 0.1 + np.random.normal(0, 0.1)
        
        rt = base_rt * equip_factor * cap_factor * recipe_factor * qty_factor
        rt = max(rt, 1)
        
        data.append({
            'equip': equip,
            'sub_equip': sub_equip,
            'capability': capability,
            'recipe': recipe,
            'qty': qty,
            'rt': rt
        })
    
    df = pd.DataFrame(data)
    
    # 进行数据分析
    analyzer = RTDataAnalyzer(df)
    analyzer.basic_statistics()
    analyzer.analyze_rt_distribution()
    analyzer.analyze_condition_combinations()
    analyzer.correlation_analysis()
    strategy = analyzer.recommend_modeling_strategy()
    
    print(f"\nRecommended modeling strategy: {strategy}")
