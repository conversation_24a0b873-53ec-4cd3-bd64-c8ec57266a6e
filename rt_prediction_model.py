import pandas as pd
import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers # type: ignore
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Tuple, Dict, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# 配置matplotlib中文字体
import matplotlib
import platform

def setup_chinese_font():
    """配置matplotlib中文字体"""
    system = platform.system()
    if system == "Windows":
        # Windows系统常用中文字体
        fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        fonts = ['Arial Unicode MS', 'Heiti TC', 'STHeiti']
    else:  # Linux
        fonts = ['DejaVu Sans', 'WenQuanYi Micro Hei', '<PERSON>m<PERSON>ei']

    for font in fonts:
        try:
            matplotlib.rcParams['font.sans-serif'] = [font]
            matplotlib.rcParams['axes.unicode_minus'] = False
            # 测试字体是否可用
            fig, ax = plt.subplots(figsize=(1, 1))
            ax.text(0.5, 0.5, '测试', fontsize=12)
            plt.close(fig)
            print(f"使用字体: {font}")
            break
        except:
            continue
    else:
        print("警告: 未找到合适的中文字体，中文可能显示为方框")
        # 使用默认字体
        matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans']
        matplotlib.rcParams['axes.unicode_minus'] = False

# 初始化字体配置
setup_chinese_font()

class RTPredictor:
    """
    运行时间预测模型
    使用深度学习预测不同条件下的运行时间(rt)
    """
    
    def __init__(self):
        self.model = None
        self.scaler_features = StandardScaler()
        self.scaler_target = StandardScaler()
        self.label_encoders = {}
        self.feature_columns = []
        self.history = None
        self.condition_stats = None
        
    def prepare_data(self, df: pd.DataFrame, is_training: bool = True) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """
        数据预处理和特征工程

        Args:
            df: 包含equip, sub_equip, capability, recipe, qty列的DataFrame，训练时还需要rt列
            is_training: 是否为训练模式

        Returns:
            处理后的特征矩阵和目标变量（预测时目标变量为None）
        """
        # 创建数据副本
        data = df.copy()

        # 1. 基础特征编码
        categorical_features = ['equip', 'sub_equip', 'capability', 'recipe']

        for feature in categorical_features:
            if feature not in self.label_encoders:
                if not is_training:
                    raise ValueError(f"Label encoder for {feature} not found. Please train the model first.")
                self.label_encoders[feature] = LabelEncoder()
                data[f'{feature}_encoded'] = self.label_encoders[feature].fit_transform(data[feature].astype(str))
            else:
                # 处理未见过的类别
                known_classes = set(self.label_encoders[feature].classes_)
                data[feature] = data[feature].astype(str)
                unknown_mask = ~data[feature].isin(known_classes)
                if unknown_mask.any():
                    print(f"Warning: Unknown categories found in {feature}: {data.loc[unknown_mask, feature].unique()}")
                    # 将未知类别替换为第一个已知类别
                    data.loc[unknown_mask, feature] = self.label_encoders[feature].classes_[0]
                data[f'{feature}_encoded'] = self.label_encoders[feature].transform(data[feature])

        # 2. 数值特征
        data['qty_normalized'] = data['qty'] / 25.0  # 归一化到0-1

        # 3. 组合特征工程
        # 为每个条件组合创建统计特征
        condition_cols = ['equip', 'sub_equip', 'capability', 'recipe']
        data['condition_key'] = data[condition_cols].astype(str).agg('_'.join, axis=1)

        if is_training and 'rt' in data.columns:
            # 训练模式：计算历史统计特征
            condition_stats = data.groupby('condition_key')['rt'].agg([
                'mean', 'median', 'std', 'min', 'max', 'count'
            ]).reset_index()
            condition_stats.columns = ['condition_key', 'hist_rt_mean', 'hist_rt_median',
                                     'hist_rt_std', 'hist_rt_min', 'hist_rt_max', 'hist_count']

            # 填充缺失的标准差
            condition_stats['hist_rt_std'] = condition_stats['hist_rt_std'].fillna(0)

            # 保存统计信息供预测时使用
            self.condition_stats = condition_stats
        else:
            # 预测模式：使用训练时保存的统计信息
            if not hasattr(self, 'condition_stats') or self.condition_stats is None:
                # 如果没有统计信息，创建默认值
                unique_conditions = data['condition_key'].unique()
                condition_stats = pd.DataFrame({
                    'condition_key': unique_conditions,
                    'hist_rt_mean': [10.0] * len(unique_conditions),  # 默认值
                    'hist_rt_median': [10.0] * len(unique_conditions),
                    'hist_rt_std': [1.0] * len(unique_conditions),
                    'hist_rt_min': [5.0] * len(unique_conditions),
                    'hist_rt_max': [20.0] * len(unique_conditions),
                    'hist_count': [10] * len(unique_conditions)
                })
                print("Warning: No historical statistics available, using default values")
            else:
                condition_stats = self.condition_stats

        # 合并统计特征
        data = data.merge(condition_stats, on='condition_key', how='left')

        # 4. qty相关的交互特征
        data['qty_squared'] = data['qty'] ** 2
        data['qty_log'] = np.log1p(data['qty'])

        # 与历史均值的比率特征
        data['qty_vs_hist_mean'] = data['qty'] / (data['hist_rt_mean'] / data['qty'] + 1e-8)

        # 5. 选择最终特征
        feature_cols = [
            'equip_encoded', 'sub_equip_encoded', 'capability_encoded', 'recipe_encoded',
            'qty', 'qty_normalized', 'qty_squared', 'qty_log',
            'hist_rt_mean', 'hist_rt_median', 'hist_rt_std', 'hist_rt_min',
            'hist_rt_max', 'hist_count', 'qty_vs_hist_mean'
        ]

        # 处理缺失值
        for col in feature_cols:
            if col in data.columns:
                if data[col].isna().any():
                    if col.startswith('hist_'):
                        # 历史统计特征使用默认值
                        default_values = {
                            'hist_rt_mean': 10.0, 'hist_rt_median': 10.0, 'hist_rt_std': 1.0,
                            'hist_rt_min': 5.0, 'hist_rt_max': 20.0, 'hist_count': 10
                        }
                        data[col] = data[col].fillna(default_values.get(col, data[col].median()))
                    else:
                        data[col] = data[col].fillna(data[col].median())

        if is_training:
            self.feature_columns = feature_cols

        X = np.array(data[feature_cols].values)

        if is_training and 'rt' in data.columns:
            y = np.array(data['rt'].values)
            return X, y
        else:
            return X, None
    
    def build_model(self, input_dim: int) -> keras.Model:
        """
        构建深度神经网络模型
        
        Args:
            input_dim: 输入特征维度
            
        Returns:
            编译后的Keras模型
        """
        model = keras.Sequential([
            # 输入层
            layers.Input(shape=(input_dim,)),
            
            # 第一层：较大的隐藏层
            layers.Dense(256, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.3),
            
            # 第二层
            layers.Dense(128, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.2),
            
            # 第三层
            layers.Dense(64, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.1),
            
            # 第四层
            layers.Dense(32, activation='relu'),
            layers.Dropout(0.1),
            
            # 输出层
            layers.Dense(1, activation='linear')
        ])
        
        # 编译模型
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        
        return model
    
    def train(self, df: pd.DataFrame, test_size: float = 0.2, 
              validation_split: float = 0.2, epochs: int = 100, 
              batch_size: int = 32) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            df: 训练数据
            test_size: 测试集比例
            validation_split: 验证集比例
            epochs: 训练轮数
            batch_size: 批次大小
            
        Returns:
            训练结果字典
        """
        # 数据预处理
        X, y = self.prepare_data(df, is_training=True)
        
        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=42, stratify=None
        )
        
        # 特征标准化
        X_train_scaled = self.scaler_features.fit_transform(X_train)
        X_test_scaled = self.scaler_features.transform(X_test)
        
        # 目标变量标准化（可选，有助于训练稳定性）
        y_train_scaled = self.scaler_target.fit_transform(y_train.reshape(-1, 1)).flatten()
        y_test_scaled = self.scaler_target.transform(y_test.reshape(-1, 1)).flatten()
        
        # 构建模型
        self.model = self.build_model(X_train_scaled.shape[1])
        
        # 回调函数
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_loss', patience=15, restore_best_weights=True
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss', factor=0.5, patience=8, min_lr=1e-6 # type: ignore
            )
        ]
        
        # 训练模型
        self.history = self.model.fit(
            X_train_scaled, y_train_scaled,
            validation_split=validation_split,
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1  # type: ignore
        )
        
        # 预测和评估
        y_pred_scaled = self.model.predict(X_test_scaled)
        y_pred = self.scaler_target.inverse_transform(y_pred_scaled).flatten()
        
        # 计算评估指标
        mae = mean_absolute_error(y_test, y_pred)
        mse = mean_squared_error(y_test, y_pred)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_test, y_pred)
        
        results = {
            'mae': mae,
            'mse': mse,
            'rmse': rmse,
            'r2': r2,
            'y_test': y_test,
            'y_pred': y_pred
        }
        
        print(f"Model Evaluation Results:")
        print(f"MAE: {mae:.4f}")
        print(f"RMSE: {rmse:.4f}")
        print(f"R²: {r2:.4f}")
        
        return results
    
    def predict(self, df: pd.DataFrame) -> np.ndarray:
        """
        预测新数据

        Args:
            df: 包含特征的DataFrame

        Returns:
            预测的rt值
        """
        if self.model is None:
            raise ValueError("Model has not been trained yet, please call train method first")

        X, _ = self.prepare_data(df, is_training=False)
        X_scaled = self.scaler_features.transform(X)
        y_pred_scaled = self.model.predict(X_scaled)
        y_pred = self.scaler_target.inverse_transform(y_pred_scaled).flatten()

        return y_pred
    
    def plot_training_history(self):
        """绘制训练历史"""
        if self.history is None:
            print("No training history to plot")
            return

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))

        # 损失曲线
        ax1.plot(self.history.history['loss'], label='Training Loss')
        ax1.plot(self.history.history['val_loss'], label='Validation Loss')
        ax1.set_title('Model Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()

        # MAE曲线
        ax2.plot(self.history.history['mae'], label='Training MAE')
        ax2.plot(self.history.history['val_mae'], label='Validation MAE')
        ax2.set_title('Model MAE')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('MAE')
        ax2.legend()

        plt.tight_layout()
        plt.show()
    
    def save_model(self, filepath: str):
        """保存模型"""
        if self.model is None:
            raise ValueError("Model has not been trained yet")

        # 保存模型架构和权重
        self.model.save(f"{filepath}_model.h5")

        # 保存预处理器
        import joblib
        joblib.dump({
            'scaler_features': self.scaler_features,
            'scaler_target': self.scaler_target,
            'label_encoders': self.label_encoders,
            'feature_columns': self.feature_columns,
            'condition_stats': self.condition_stats
        }, f"{filepath}_preprocessors.pkl")

        print(f"Model saved to {filepath}")

    def load_model(self, filepath: str):
        """加载模型"""
        # 加载模型
        self.model = keras.models.load_model(f"{filepath}_model.h5")

        # 加载预处理器
        import joblib
        preprocessors = joblib.load(f"{filepath}_preprocessors.pkl")
        self.scaler_features = preprocessors['scaler_features']
        self.scaler_target = preprocessors['scaler_target']
        self.label_encoders = preprocessors['label_encoders']
        self.feature_columns = preprocessors['feature_columns']
        self.condition_stats = preprocessors.get('condition_stats', None)

        print(f"Model loaded from {filepath}")


# 使用示例
if __name__ == "__main__":
    # 创建示例数据
    np.random.seed(42)
    
    # 模拟数据生成
    n_samples = 1000
    equips = ['EQ001', 'EQ002', 'EQ003', 'EQ004']
    sub_equips = ['SUB_A', 'SUB_B', 'SUB_C']
    capabilities = ['CAP_HIGH', 'CAP_MED', 'CAP_LOW']
    recipes = ['RECIPE_1', 'RECIPE_2', 'RECIPE_3', 'RECIPE_4', 'RECIPE_5']
    
    data = []
    for _ in range(n_samples):
        equip = np.random.choice(equips)
        sub_equip = np.random.choice(sub_equips)
        capability = np.random.choice(capabilities)
        recipe = np.random.choice(recipes)
        qty = np.random.randint(1, 26)
        
        # 模拟rt与各因素的关系
        base_rt = 10  # 基础时间
        equip_factor = {'EQ001': 1.0, 'EQ002': 1.2, 'EQ003': 0.9, 'EQ004': 1.1}[equip]
        cap_factor = {'CAP_HIGH': 0.8, 'CAP_MED': 1.0, 'CAP_LOW': 1.3}[capability]
        recipe_factor = np.random.uniform(0.8, 1.5)
        qty_factor = qty * 0.1 + np.random.normal(0, 0.1)
        
        rt = base_rt * equip_factor * cap_factor * recipe_factor * qty_factor
        rt = max(rt, 1)  # 确保rt为正数
        
        data.append({
            'equip': equip,
            'sub_equip': sub_equip,
            'capability': capability,
            'recipe': recipe,
            'qty': qty,
            'rt': rt
        })
    
    df = pd.DataFrame(data)
    print("Sample data:")
    print(df.head())
    print(f"\nData shape: {df.shape}")

    # 训练模型
    predictor = RTPredictor()
    results = predictor.train(df, epochs=50)

    # 绘制训练历史
    predictor.plot_training_history()

    # 预测示例
    test_data = pd.DataFrame([
        {'equip': 'EQ001', 'sub_equip': 'SUB_A', 'capability': 'CAP_HIGH', 'recipe': 'RECIPE_1', 'qty': 15},
        {'equip': 'EQ002', 'sub_equip': 'SUB_B', 'capability': 'CAP_MED', 'recipe': 'RECIPE_2', 'qty': 20}
    ])

    predictions = predictor.predict(test_data)
    print(f"\nPrediction results:")
    for i, pred in enumerate(predictions):
        print(f"Sample {i+1}: predicted rt = {pred:.2f}")
