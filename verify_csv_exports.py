"""
验证所有CSV导出功能都使用outputs文件夹
"""

import os
import pandas as pd
from rt_prediction_model import RTPredictor, generate_sample_data, get_output_path, ensure_outputs_dir

def test_csv_export_paths():
    """测试CSV导出路径是否正确使用outputs文件夹"""
    print("=== Testing CSV Export Paths ===")
    
    # 确保outputs文件夹存在
    ensure_outputs_dir()
    
    # 测试get_output_path函数
    test_paths = [
        get_output_path("test1.csv", "predictions"),
        get_output_path("test2.csv", "test_data"),
        get_output_path("test3.csv"),  # 无子文件夹
    ]
    
    print("Generated paths:")
    for path in test_paths:
        print(f"  {path}")
        # 验证路径包含outputs
        if "outputs" in path:
            print(f"    ✓ Uses outputs folder")
        else:
            print(f"    ⚠ Does NOT use outputs folder")
    
    return all("outputs" in path for path in test_paths)

def test_predict_missing_qty_export():
    """测试predict_missing_qty.py的导出功能"""
    print("\n=== Testing predict_missing_qty.py Export ===")
    
    try:
        from predict_missing_qty import predict_missing_qty_for_condition
        
        # 创建简单的测试数据
        df = generate_sample_data(50)
        predictor = RTPredictor()
        predictor.train(df, epochs=2, batch_size=16)  # 快速训练
        
        # 测试数据
        existing_data = {1: 8.5, 5: 15.2, 10: 22.1}
        
        # 调用函数（不指定export_path，应该使用默认的outputs路径）
        result_df = predict_missing_qty_for_condition(
            equip='EQ001',
            sub_equip='SUB_A', 
            capability='CAP_HIGH',
            recipe='RECIPE_1',
            existing_data=existing_data,
            predictor=predictor,
            export_path=None  # 使用默认路径
        )
        
        print("✓ predict_missing_qty_for_condition completed")
        return True
        
    except Exception as e:
        print(f"✗ Error in predict_missing_qty test: {e}")
        return False

def test_example_usage_export():
    """测试example_usage.py的导出功能"""
    print("\n=== Testing example_usage.py Export Functions ===")
    
    try:
        from example_usage import predict_missing_qty_for_conditions, predict_specific_condition_missing_qty
        
        # 创建测试数据
        df = generate_sample_data(50)
        predictor = RTPredictor()
        predictor.train(df, epochs=2, batch_size=16)  # 快速训练
        
        # 测试predict_missing_qty_for_conditions
        target_conditions = [
            {'equip': 'EQ001', 'sub_equip': 'SUB_A', 'capability': 'CAP_HIGH', 'recipe': 'RECIPE_1'}
        ]
        
        result1 = predict_missing_qty_for_conditions(
            df, predictor, target_conditions, export_path=None
        )
        print("✓ predict_missing_qty_for_conditions completed")
        
        # 测试predict_specific_condition_missing_qty
        result2 = predict_specific_condition_missing_qty(
            equip='EQ001',
            sub_equip='SUB_A',
            capability='CAP_HIGH',
            recipe='RECIPE_1',
            existing_qtys=[1, 5, 10],
            existing_rts=[8.5, 15.2, 22.1],
            predictor=predictor,
            export_path=None
        )
        print("✓ predict_specific_condition_missing_qty completed")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in example_usage test: {e}")
        return False

def check_outputs_folder_structure():
    """检查outputs文件夹结构"""
    print("\n=== Checking Outputs Folder Structure ===")
    
    outputs_dir = "outputs"
    expected_subdirs = ["models", "plots", "predictions", "test_data"]
    
    if not os.path.exists(outputs_dir):
        print(f"⚠ {outputs_dir} folder does not exist")
        return False
    
    print(f"✓ {outputs_dir} folder exists")
    
    # 检查子文件夹
    for subdir in expected_subdirs:
        subdir_path = os.path.join(outputs_dir, subdir)
        if os.path.exists(subdir_path):
            print(f"✓ {subdir_path} exists")
        else:
            print(f"⚠ {subdir_path} does not exist")
    
    # 列出所有文件
    print(f"\nContents of {outputs_dir}:")
    for root, dirs, files in os.walk(outputs_dir):
        level = root.replace(outputs_dir, "").count(os.sep)
        indent = " " * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = " " * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")
    
    return True

def show_csv_export_summary():
    """显示CSV导出功能总结"""
    print("\n=== CSV Export Functions Summary ===")
    
    functions_info = [
        {
            "file": "predict_missing_qty.py",
            "function": "predict_missing_qty_for_condition()",
            "default_path": "outputs/predictions/prediction_results_{equip}_{sub_equip}_{capability}_{recipe}_{timestamp}.csv",
            "status": "✓ Fixed"
        },
        {
            "file": "example_usage.py", 
            "function": "predict_missing_qty_for_conditions()",
            "default_path": "outputs/predictions/prediction_results_{timestamp}.csv",
            "status": "✓ Fixed"
        },
        {
            "file": "example_usage.py",
            "function": "predict_specific_condition_missing_qty()",
            "default_path": "outputs/predictions/prediction_{equip}_{sub_equip}_{capability}_{recipe}_{timestamp}.csv",
            "status": "✓ Fixed"
        },
        {
            "file": "rt_prediction_model.py",
            "function": "save_model()",
            "default_path": "outputs/models/{model_name}_model.h5 & {model_name}_preprocessors.pkl",
            "status": "✓ Fixed"
        },
        {
            "file": "rt_prediction_model.py",
            "function": "plot_training_history()",
            "default_path": "outputs/plots/training_history_{timestamp}.png",
            "status": "✓ Fixed"
        }
    ]
    
    print(f"{'Function':<40} {'Default Output Path':<60} {'Status'}")
    print("-" * 110)
    
    for info in functions_info:
        print(f"{info['function']:<40} {info['default_path']:<60} {info['status']}")
    
    print("\n✓ All CSV export functions now use outputs folder by default")
    print("✓ Users can still specify custom paths if needed")
    print("✓ Automatic timestamp prevents file overwriting")

if __name__ == "__main__":
    print("Verifying CSV Export Functions Use Outputs Folder")
    print("=" * 60)
    
    # 运行测试
    test1 = test_csv_export_paths()
    test2 = test_predict_missing_qty_export()
    test3 = test_example_usage_export()
    test4 = check_outputs_folder_structure()
    
    # 显示总结
    show_csv_export_summary()
    
    print("\n" + "=" * 60)
    print("Verification Results:")
    print(f"Path Generation: {'✓ PASS' if test1 else '⚠ FAIL'}")
    print(f"predict_missing_qty.py: {'✓ PASS' if test2 else '⚠ FAIL'}")
    print(f"example_usage.py: {'✓ PASS' if test3 else '⚠ FAIL'}")
    print(f"Outputs Structure: {'✓ PASS' if test4 else '⚠ FAIL'}")
    
    if all([test1, test2, test3, test4]):
        print("\n🎉 All CSV exports now correctly use outputs folder!")
    else:
        print("\n⚠ Some issues found, please check the details above.")
    
    print("\nNext steps:")
    print("1. Run example_usage.py to test full functionality")
    print("2. Check outputs/predictions/ for CSV files")
    print("3. Check outputs/models/ for model files")
    print("4. Check outputs/plots/ for training plots")
